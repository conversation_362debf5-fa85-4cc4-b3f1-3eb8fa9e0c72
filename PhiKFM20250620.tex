%% 
%% Copyright 2007-2025 Elsevier Ltd
%% 
%% This file is part of the 'Elsarticle Bundle'.
%% ---------------------------------------------
%% 
%% It may be distributed under the conditions of the LaTeX Project Public
%% License, either version 1.3 of this license or (at your option) any
%% later version.  The latest version of this license is in
%%    http://www.latex-project.org/lppl.txt
%% and version 1.3 or later is part of all distributions of LaTeX
%% version 1999/12/01 or later.
%% 
%% The list of all files belonging to the 'Elsarticle Bundle' is
%% given in the file `manifest.txt'.
%% 
%% Template article for Elsevier's document class `elsarticle'
%% with numbered style bibliographic references
%% SP 2008/03/01
%% $Id: elsarticle-template-num.tex 272 2025-01-09 17:36:26Z rishi $
%%
\documentclass[preprint,12pt]{elsarticle}

%% Use the option review to obtain double line spacing
%% \documentclass[authoryear,preprint,review,12pt]{elsarticle}

%% Use the options 1p,twocolumn; 3p; 3p,twocolumn; 5p; or 5p,twocolumn
%% for a journal layout:
%% \documentclass[final,1p,times]{elsarticle}
%% \documentclass[final,1p,times,twocolumn]{elsarticle}
%% \documentclass[final,3p,times]{elsarticle}
%% \documentclass[final,3p,times,twocolumn]{elsarticle}
%% \documentclass[final,5p,times]{elsarticle}
%% \documentclass[final,5p,times,twocolumn]{elsarticle}

%% For including figures, graphicx.sty has been loaded in
%% elsarticle.cls. If you prefer to use the old commands
%% please give \usepackage{epsfig}

%% The amssymb package provides various useful mathematical symbols
\usepackage{amssymb}
%% The amsmath package provides various useful equation environments.
\usepackage{amsmath}
%% The amsthm package provides extended theorem environments
%% \usepackage{amsthm}
%% Algorithm packages for algorithm formatting
\usepackage{algorithm}
\usepackage{algorithmic}

%% The lineno packages adds line numbers. Start line numbering with
%% \begin{linenumbers}, end it with \end{linenumbers}. Or switch it on
%% for the whole article with \linenumbers.
%% \usepackage{lineno}

\journal{Nuclear Physics B}

\begin{document}

\begin{frontmatter}

%% Title, authors and addresses

%% use the tnoteref command within \title for footnotes;
%% use the tnotetext command for theassociated footnote;
%% use the fnref command within \author or \affiliation for footnotes;
%% use the fntext command for theassociated footnote;
%% use the corref command within \author for corresponding author footnotes;
%% use the cortext command for theassociated footnote;
%% use the ead command for the email address,
%% and the form \ead[url] for the home page:
%% \title{Title\tnoteref{label1}}
%% \tnotetext[label1]{}
%% \author{Name\corref{cor1}\fnref{label2}}
%% \ead{email address}
%% \ead[url]{home page}
%% \fntext[label2]{}
%% \cortext[cor1]{}
%% \affiliation{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}
%% \fntext[label3]{}

\title{$\Phi$KFM: Physically-Constrained Sign Language Generation via Attention-LSTM and Differentiable Kalman Filtering}

%% use optional labels to link authors explicitly to addresses:
%% \author[label1,label2]{}
%% \affiliation[label1]{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}
%%
%% \affiliation[label2]{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}

\author[label1]{Qinkun Xiao} %% Author name
\author[label2]{Peiran Liu}
\author[label2]{Lu Li}
\author{Jielei Xiao}
%% Author affiliation
\affiliation[label1]{organization={Department of Electrical and Information Engineering, Xi'an Technological University},%Department and Organization
            addressline={No.2 Xuefu Middle Road}, 
            city={Xi'an},
            postcode={}, 
            state={Shaanxi},
            country={China}}

%% Author affiliation
\affiliation[label2]{organization={Department of Mechanical and Electrical Engineering, Xi'an Technological University},%Department and Organization
            addressline={No.2 Xuefu Middle Road}, 
            city={Xi'an},
            postcode={}, 
            state={Shaanxi},
            country={China}}
                

%% Abstract
\begin{abstract}
We present $\Phi$KFM, a novel sign language generation (SLG) framework that combines an attention-enhanced with differentiable Kalman filtering model (KFM) to address linguistic fidelity, biomechanical compliance, and spatiotemporal coherence. $\Phi$KFM  explicitly integrates physical constraints through parallel LSTM streams for kinematic synchronization and probabilistic filtering, eliminating the need for post-processing. Our composite loss function, balancing KL divergence and biomechanical penalties, achieves a joint compliance rate of 91.8\% (a 3.7\% improvement) and reduces acceleration discontinuities by 62.7\%. Experimental results demonstrate superior motion naturalness (MOS: 4.7/5.0) with only a 14ms computational overhead. Key innovations include: 1) physics-aware attention for motion-text alignment, 2) differentiable filtering to enforce anatomical constraints, and 3) adaptive curriculum learning. $\Phi$KFM advances assistive communication and establishes new standards for physically-constrained motion generation.
\end{abstract}

%%Graphical abstract
\begin{graphicalabstract}
\centering
\includegraphics[width=\textwidth]{Graphical Abstract.pdf}
\end{graphicalabstract}

%%Research highlights
\begin{highlights}
\item Introduces $\Phi$KFM, a physics-aware SLG model with Kalman-filter-guided LSTM.
\item Enforces biomechanical constraints in real time using differentiable filtering.
\item Achieves 91.8\% joint compliance and reduces acceleration errors by 62.7\%.
\item Delivers high motion naturalness (MOS 4.7/5.0) with only 14ms overhead.
\item Demonstrates strong cross-domain generalization on CSL500, WLASL, and Kinetics.
\end{highlights}

%% Keywords
\begin{keyword}
SLG \sep KFM \sep biomechanical compliance \sep LSTM


\end{keyword}

\end{frontmatter}

%% Add \usepackage{lineno} before \begin{document} and uncomment 
%% following line to enable line numbers
%% \linenumbers

%% main text
%%

%% Use \section commands to start a section
\section{Introduction}
\label{sec1}
%% Labels are used to cross-reference an item using \ref command.
As the inverse process of sign language recognition (SLR), sign language generation (SLG) aims to translate linguistic content into semantically accurate and biomechanically plausible sign sequences \cite{ref1}. Despite substantial progress in SLR, where modern approaches achieve recognition accuracies exceeding 92\% in controlled environments \cite{ref2}, SLG still faces persistent challenges in linguistics, biomechanics, and computer science \cite{ref3}.

An effective SLG system must simultaneously address three interconnected challenges. First, linguistic fidelity must be preserved, since sign languages possess unique grammar and syntax that differ significantly from spoken languages \cite{ref4}. These complex spatial-kinematic systems involve the precise coordination of handshapes, movements, spatial locations, and facial expressions \cite{ref5}. Early rule-based systems employed finite state machines and kinematic interpolation \cite{ref6} but lacked flexibility to accommodate natural variability \cite{ref7}. Although modern deep learning techniques have improved semantic representation \cite{ref8}, maintaining grammatical accuracy while capturing nuanced natural variations remains a significant challenge \cite{ref9}.

Additionally, biomechanical compliance is essential due to strict physiological constraints often violated by current generative models \cite{ref10}. These constraints encompass joint angle ranges (e.g., finger joints typically 0°-90°) \cite{ref11}, velocity thresholds (e.g., elbow velocities below 1.5 m/s) \cite{ref12} , characteristic acceleration patterns (e.g., damped harmonic motion of the wrist) \cite{ref13}, and principles of energy minimization \cite{ref14}. According to the ASL-Physics 2023 benchmark, approximately 34\% of generated joint angles exceed biomechanical limits \cite{ref15}, resulting in unnatural or physically implausible movements \cite{ref16}, which significantly undermines communicative clarity \cite{ref17}.

Furthermore, achieving spatiotemporal coherence—which entails precise temporal coordination and smooth motion transitions—remains a major challenge \cite{ref18}. Current systems frequently produce discontinuous motion, with a discontinuity rate 58\% higher than that of human signers, as observed in the SignGen-2023 dataset \cite{ref19}. This issue stems from insufficient modeling of inter-articular dependencies \cite{ref20}, limited phrase-level contextual windows \cite{ref21}, and inconsistencies between training and inference conditions \cite{ref22}. These discontinuities hinder both the naturalness and intelligibility of the generated motion, as rhythmic features of signing convey essential linguistic cues \cite{ref23}.

SLG methodologies have undergone significant evolution over the past decades \cite{ref24}. Early rule-based approaches relied heavily on finite state machines, handcrafted databases, and kinematic interpolation techniques \cite{ref25}, offering precise control yet lacking flexibility \cite{ref26}. Statistical learning techniques (2015-2020), including Hidden Markov Models \cite{ref27}, Dynamic Time Warping \cite{ref28}, and Gaussian Mixture Models \cite{ref29}, improved generalizability but were inadequate in handling the high dimensionality and complexity of sign language data. Recent deep learning approaches include LSTM architectures effective in temporal modeling yet prone to error accumulation, Transformer-based models that handle long-range dependencies but incur higher computational complexity, and generative models like GANs, VAEs, and diffusion models, which often produce physically implausible motions.

Specialized techniques for enforcing biomechanical constraints—such as post-processing filters, Lagrangian neural networks, and physics-inspired loss functions—have shown promise in improving motion plausibility. However, they often introduce computational latency and demand extensive parameter tuning. Moreover, these methods typically regard physical constraints as auxiliary components, rather than integrating them into the core generation process.

In response, this paper introduces $\Phi$KFM, a novel SLG framework that fundamentally rethinks the integration of physical constraints into the generative architecture. $\Phi$KFM combines an attention-enhanced LSTM with a differentiable Kalman filtering model to simultaneously address linguistic fidelity, biomechanical compliance, and spatiotemporal coherence. The framework employs a dual-stream synchronization mechanism that jointly models joint coordinates and kinematic states, while probabilistic filtering ensures real-time enforcement of physiological constraints. Additionally, a composite loss function—blending KL divergence, biomechanical penalties, and coherence metrics—guides training under an adaptive curriculum, offering both efficiency and robustness.

Implementation advantages include exceptional computational efficiency with minimal overhead (14ms per frame) and robust generalization across CSL500, WLASL, and Kinetics datasets, demonstrating improvements in joint compliance (91.8\%, +3.7\% over baselines), reduced acceleration discontinuities (62.7\%), and temporal naturalness (mean opinion scores of 4.7/5.0). Qualitative assessments further confirm enhancements in elbow trajectory realism, finger coordination, and phrase-level rhythm.

Beyond technical contributions, $\Phi$KFM supports accessible communication technologies, improves SL avatars, and enhances educational applications. It also opens new research pathways in physics-informed generative models and real-time constrained optimization, advancing telepresence, digital interpretation, and VR/AR platforms. This work marks a significant paradigm shift by placing biomechanical constraints at the core of the generative process, demonstrating notable improvements in both objective metrics and subjective naturalness assessments. Future developments will explore extending $\Phi$KFM to full-body signing and interactive systems, further promoting accessible communication technologies.


%% Use \subsection commands to start a subsection.
\section{Methods}
\subsection{Overview}
\label{subsec1}

The overall framework of proposed method is illustrated in Fig.\ref{fig1} The main novelty of our approach is to combine deep neural network (DNN) sequence generation models and probabilistic graphical models (PGM) for generating SL obeyed real-world physical motion laws. The proposed model contains two main components: the DNN and the PGM, we call the fused model $\Phi$KFM. The DNN includes encoder, attention, and decoder modules. In DNN, the input is a text sequence $\mathbf{S}=(s_1,\cdots,s_n)$, and the decoder outputs an initial estimated SL skeleton sequence $\hat{\mathbf{K}}=({\hat{k}}_1,\cdots,{\hat{k}}_T)$. In PGM, the Kalman filtering model (KFM) is used for skeleton data updating, yielding an updated skeleton sequence $\mathbf{X}=(\mathbf{x}_1,\cdots,\mathbf{x}_T)$, that conforms to real-world physical motion laws. The loss function $\mathcal{L}$ is the distribution distance between the true skeleton sequence $\mathbf{K}^{real}$ corresponding to the text $\mathbf{S}$ and the optimal generated skeleton sequence $\mathbf{X}$. In system learning, we consider the loss function with physical constraints ($\mathbf{U}$) as the optimization objective.

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_1.pdf}
\caption{The proposed DNN-PGM SLG framework ($\Phi$KFM). (a) The training of $\Phi$KFM. In the part of DNN, includes 3 modules: the encoder $(f_e)$, the attention module $(f_{att})$, the decoder $(f_d)$. In the part of PGM, we use a KFM, which is a probability graph model, to optimize the initial generated skeleton. The system can generate optimal skeleton sequence $\mathbf{X}$ obeyed the real-world physical motion laws. The loss function $\mathcal{L}$ is the combination of KL distance $(\mathcal{L}_{KL})$ and physical constraints $(\mathcal{L}_{phy})$ given by inputted control $\mathbf{U}$. (b) The testing of model. To build a SL discriminator for testing the classification accuracy of generated SL.}\label{fig1}
\end{figure}

\subsection{Module Design}
\subsubsection{Deep Network}
The first and basic component is encoder ($f_e$), as shown in  Fig.\ref{fig1}(a). The module encodes the text sequence $\mathbf{S}=(s_1,\cdots,s_T)$ into the feature sequence $\mathbf{h}=(h_1,\cdots,h_T)$. We use a BiLSTM network as the encoder, and our model can effectively represent the context information. When $\mathbf{S} $ is fed into encoder $f_e$, we have:

\begin{equation}\label{eq1}
\left(h_1,\cdots,h_T\right)=f_e(\mathbf{S},h_0;\theta_e)
\end{equation}

where $h_0$ is the initial given data, $\theta_e$ is the encoder parameter, and $h_T$ is feature that contains the global information of $\mathbf{S}$. 
The attention module $f_{att}$ calculates the attention coefficient $\alpha_{ti}$ and the context vector $cv_t$ of the input skeleton sequence. Let $h_i$ be a feature of $k_i$, we have:

\begin{equation}\label{eq2}
{cv}_t=\sum_{i=1}^{T}{\alpha_{ti}h_i}
\end{equation}

The decoder $f_d$ decodes the $\mathbf{h}$ to generate the skeleton sequence $\hat{\mathbf{K}}$.  To obtain conditional-based generation results. This paper uses a single-layer LSTM network as the decoder. An LSTM-based decoder unit can be represented as:

\begin{equation}\label{eq3}
\left[{\hat{k}}_t,h_t^d\right]=U_d^{LSTM}({\hat{k}}_{t-1},h_{t-1}^d,{cv}_t),                      
\end{equation}

where $U_d^{LSTM}$ is the basic decoder unit of decoder. If $h_0^d=\mathbf{h}$ the calculation of the decoder is: 

\begin{equation}\label{eq4}
\hat{\mathbf{K}}=\left({\hat{k}}_1,\cdots,{\hat{k}}_T\right)=f_d\left(\mathbf{h},\mathbf{CV};\theta_d\right),        
\end{equation}

where $f_d$ is the decoder, and $\theta_d$ is the decoder parameter, the $\mathbf{CV}=({cv}_1,\cdots,{cv}_T)$, $\hat{\mathbf{K}}$ is the generated skeleton sequence. 

\subsubsection{Dynamic Bayesian Network}
In our system, we use a kind of dynamic Bayesian network (DBN) model to update the generated skeleton data $(\hat{\mathbf{K}})$, which is called as KFM. In KFM, observation sequence is the $\hat{\mathbf{K}}=\{\hat{k}_t\}_{t=1}^T$ (produced by DNN), SL physical constraint sequence is $\mathbf{U}=\{u_t\}_{t=1}^T$, state sequence is $\mathbf{X}=\{\mathbf{x}_t\}_{t=1}^T$. The $\hat{k}_t=(J_{t,i})_{i=1}^n$ is estimated value of generated skeleton data in time $t$ $(1<t<T)$, and where the $J_{t,i}$ is the coordinate value of $i$-th joint in time $t$. The $\mathbf{x}_t$ is state value of filter, which related to the motion state of skeleton, such as velocity and acceleration of joints in skeleton, hence, the $\mathbf{x}_t$ can be written as:

\begin{equation}\label{eq5}
\mathbf{x}_t=[(J_{t,i})_{i=1}^n,({\dot{J}}_{t,i})_{i=1}^n,({\ddot{J}}_{t,i})_{i=1}^n]T,                       
\end{equation}

where ${\dot{J}}_{t,i}$ and ${\ddot{J}}_{t,i}$ are velocity and acceleration of $i-th$ joint in time t. The $u_t$ is the physical constraint in time t, then physical constraint is: (1) $J_{t,i}-J_{t-1,i}<\varepsilon_1$, and (2) $J_{t,i}-J_{t-1,i}<\varepsilon_2$, (3) $\theta_{min}\le\theta_{t,i}\le\theta_{max}$, as shown in ~Fig.\ref{fig2}. In order to integrate discrete inequality constraints into a continuous KFM probabilistic framework, we have:

\begin{equation}\label{eq6}
u_t=\begin{pmatrix}
\{\max{J_{t,i}-J_{t-1,i}-\varepsilon_1,0}\}_{i=1}^n \\
\{\max{J_{t,i}-J_{t-1,i}-\varepsilon_2,0}\}_{i=1}^n \\
\{\mathbb{I} (\theta_{t,i}\notin[\theta_{\min},\theta_{\max}])\}_{i=1}^n
\end{pmatrix}.
\end{equation}

The filtering calculation is key step for skeleton data updating. The details are described as follow. Firstly, the filtering calculation is a recurrent calculation: 

\begin{equation}\label{eq7}
p\left({\mathbf{x}_{t+1}|\hat{k}}_{1:t+1},u_{1:t+1}\right)=f({\hat{k}}_{t+1},p\left({\mathbf{x}_t|\hat{k}}_{1:t},u_{1:t}\right)),       
\end{equation}

where $f(.)$ is filter function, and ${\hat{k}}_t=(J_{t,i})_{i=1}^n$ is estimated value of generated skeleton data in time t$ (1<t<T)$, which can be seen as the observed data of filter.
According to Bayes rule and based on Eqs.\ref{eq5}, we have:

\begin{align}\label{eq8}
p\left({\mathbf{x}_{t+1}|\hat{k}}_{1:t+1},u_{1:t+1}\right)
&=p\left({\mathbf{x}_{t+1}|\hat{k}}_{t+1},u_{t+1},{\hat{k}}_{1:t},u_{1:t}\right) \nonumber \\
&=\alpha p\left({\hat{k}}_{t+1},u_{t+1}\middle|\mathbf{x}_{t+1},{\hat{k}}_{1:t},u_{1:t}\right)p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right) \nonumber \\
&=\alpha p\left({\hat{k}}_{t+1},u_{t+1}\middle|\mathbf{x}_{t+1}\right)p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right)
\end{align}

where $p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right)$ can be written as:

\begin{equation}\label{eq9}
p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right)=\int{p{(\mathbf{x}}_{t+1}\left|\mathbf{x}_t\right)p(\mathbf{x}_t|{\hat{k}}_{1:t},u_{1:t})}      
\end{equation}

Hence, we get the filtering function as:

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_2.pdf}
\caption{The physical constraint of SL in time $t$.}\label{fig2}
\end{figure}

\begin{align}\label{eq10}
p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t+1},u_{1:t+1}\right)
&=\alpha p\left({\hat{k}}_{t+1},u_{t+1}\middle|\mathbf{x}_{t+1}\right)p\left(\mathbf{x}_{t+1}\middle|{\hat{k}}_{1:t},u_{1:t}\right) \nonumber \\
&=\alpha p\left({\hat{k}}_{t+1},u_{t+1}\middle|\mathbf{x}_{t+1}\right)\int{p\left(\mathbf{x}_{t+1}\middle|\mathbf{x}_t\right)p(\mathbf{x}_t|{\hat{k}}_{1:t},u_{1:t})d\mathbf{x}_t}
\end{align}

The next, based on the filtering function, we can get smooth function of graph model. For at time $j$ ($1<j<t$), we have:

\begin{align}\label{eq11}
p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:t+1},u_{1:t+1}\right)
&=p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:j},u_{1:j},{\hat{k}}_{j+1:t},u_{j+1:t}\right) \nonumber \\
&=\alpha p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:j},u_{1:j}\right)p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j,{\hat{k}}_{1:j},u_{1:j}\right) \nonumber \\
&=\alpha p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:j},u_{1:j}\right)p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j\right)
\end{align}

where $p\left(\mathbf{x}_j\middle|{\hat{k}}_{1:j},u_{1:j}\right)$ is filtering part, which can be processed by Eqs.\ref{eq5}, and $p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j\right)$ is calculated by:

\begin{align}\label{eq12}
p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j\right)
&=\int{p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_j,\mathbf{x}_{j+1}\right)p\left(\mathbf{x}_{j+1}\middle|\mathbf{x}_j\right)}d\mathbf{x}_{j+1} \nonumber \\
&=\int{p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_{j+1}\right)p\left(\mathbf{x}_{j+1}\middle|\mathbf{x}_j\right)}d\mathbf{x}_{j+1} \nonumber \\
&=\int{p\left({\hat{k}}_{j+1:t},u_{j+1:t},{\hat{k}}_{j+2:t},u_{j+2:t}\middle|\mathbf{x}_{j+1}\right)p\left(\mathbf{x}_{j+1}\middle|\mathbf{x}_j\right)d\mathbf{x}_{j+1}} \nonumber \\
&=\int{p\left({\hat{k}}_{j+1:t},u_{j+1:t}\middle|\mathbf{x}_{j+1}\right)p({\hat{k}}_{j+2:t},u_{j+2:t}|\mathbf{x}_{j+1})p\left(\mathbf{x}_{j+1}\middle|\mathbf{x}_j\right)d\mathbf{x}_{j+1}}
\end{align}

From Eqs.\ref{eq3}-Eqs.\ref{eq7}, we finally get smooth function of graph model as:

\begin{align}\label{eq13}
\max_{\mathbf{x}_{1:T-1}}{p\left(\mathbf{x}_{1:T}\middle|{\hat{k}}_{1:T},u_{1:T}\right)}
&=\alpha p\left({\hat{k}}_T,u_T\middle|\mathbf{x}_T\right)\max_{\mathbf{x}_{T-1}}{p\left(\mathbf{x}_T\middle|\mathbf{x}_{T-1}\right)} \nonumber \\
&\quad \times \max_{\mathbf{x}_{1:T-2}}{p\left(\mathbf{x}_{1:T-1}\middle|{\hat{k}}_{1:T-1},u_{1:T-1}\right)}
\end{align}

In KFM, in general, we have:
\begin{equation}\label{eq14}
p\left(\mathbf{x}_t\middle|\mathbf{x}_{t-1}\right)=\mathcal{N}(A\mathbf{x}_{t-1}+Bu_{t-1},Q)
\end{equation}

which $A$ is state transform matrix, and:
\begin{equation}\label{eq15}
p\left({\hat{k}}_t,u_t\middle|\mathbf{x}_t\right)=\mathcal{N}(H\mathbf{x}_t,R)
\end{equation}

which is observation matrix, and $\pi_0=p(\mathbf{x}_0)$, which is initial state matrix. Thus, KFM system parameter is $\Omega=\{\pi_0,A,B,H,Q,R\}$. Let smooth function of graph model (eq.13) be $f_{KFM}$, we have:

\begin{equation}\label{eq16}
\mathbf{X}\gets f_{KFM}(\hat{\mathbf{K}},\mathbf{U};\Omega)
\end{equation}

\subsection{System Solution and Robustness Analysis}
We have described the framework of the system above, and in $\Phi$KFM system, two basic points should be considered. One is whether there is an optimized solution to meet our diverse SLG needs under the proposed $\Phi$KFM model, and the other is whether the $\Phi$KFM system is continuously derivable and robustness. If these properties are satisfied, we have reason to believe that the proposed model framework is domain universal and can be applied to other sequence generation domains in addition to SLG. Therefore, below we will first discuss two theorems, one is the existence theorem of the optimized solution based on this model, and the other is the continuous derivability and robustness of system.

\textbf{Theorem 1:} Let $\mathcal{X}\in\mathbb{R}^n$ denote the feasible set of produced sequences, $\mathbf{K}^{real}$ is real sequence data, $\mathbf{X}$ is estimated state of sequence data, $\mathbf{S}$ is the input text sequence, $\mathbf{U}$ is physical constraint of produced sequence, and the $\Phi$KFM-based sequence production optimization is equivalent to solve the constrained convex optimization:
\begin{equation}\label{eq17}
J(\mathbf{X})=\lambda_1D_{KL}\left(p_{real}\left(\mathbf{K}^{real}|\mathbf{S}\right)\parallel q_\Phi\left(\mathbf{X}|\mathbf{S}\right)\right)+\lambda_2\mathbf{U}
\end{equation}

where $D_{KL}$ is the Kullback-Leibler divergence, $\sum_{i}{\lambda_i=1}$, and $p_{real}$ is real skeleton sequence distribution, the $q_\Phi$ is generated skeleton sequence, the $\Phi=\{\theta_e,\theta_d,\Omega\}$. Under the $\Phi$KFM framework, the generated sequence $\mathbf{X}^\ast$ is an optimal solution of $J(\mathbf{X})$, i.e.,

\begin{equation}\label{eq18}
\mathbf{X}^\ast = \arg\min_{\mathbf{X}\in\mathcal{X}} J(\mathbf{X})
\end{equation}

\textbf{Proof:} Based on the provided the $\Phi$KFM coupling model, the definition of feasible set:$\mathcal{X}=\left\{\mathbf{X}\mid\theta_{min}\le\theta_{t,m}\le\theta_{max},\forall t,m\right\}\subset\mathbb{R}^n$

where $n = 3\times M\times T$ represents the skeleton sequence dimension, $M$ is the number of joints, and $\theta_{t,m}$ denotes the joint angle of the $m$-th joint at frame $t$.

(1) Convexity of Objective Function $J(\mathbf{X})$

Firstly, convexity of $\mathcal{X}$ can be discussed. For any $\mathbf{X}^{(1)},\mathbf{X}^{(2)}\in\mathcal{X}$ and $\alpha\in[0,1]$, the linear combination: $\mathbf{X}^{(\lambda)}=\alpha\mathbf{X}^{(1)}+\left(1-\alpha\right)\mathbf{X}^{(2)}$ satisfies:

\begin{equation}\label{eq19}
\theta_{min}\le\alpha\theta_{t,j}^{(1)}+(1-\alpha)\theta_{t,j}^{(2)}\le\theta_{max},\forall t,j
\end{equation}

Since $\theta_{min}$ and $\theta_{max}$ are constants, the inequality remains closed under convex combinations. Velocity and acceleration constraints are affine transformations of joint angles. The intersection of convex sets preserves convexity. Thus, $\mathcal{X}$ is a convex set.

Secondly, to decomposed of objective function Eqs.\ref{eq17}, we know, the $q_\Phi$ is distribution from DNN-PGM. When $p_{real}$ is fixed and $q_\Phi$ is an exponential family distribution (e.g., Gaussian), the KL divergence is convex in the parameters of $p_{real}$. The next, in Eqs.\ref{eq6}, the $\lambda\mathbf{U}$ is a quadratic function, which is strictly convex. Overall, the non-negative linear combination of convex functions preserves convexity. Hence, $J(\mathbf{X})$ is convex over $\mathcal{X}$.

(2) Existence of optimal solution ($\mathbf{X}^\ast$)

$\mathcal{X}$ is closed (due to inequality constraints) and bounded (joint angles are bounded), making it compact. $J(\mathbf{X})$ is continuous within $\mathcal{X}$. By the Weierstrass Extreme Value Theorem, a lower semi-continuous function on a compact set attains its minimum. Hence, there is a $\mathbf{X}^\ast\in\mathcal{X}$ to make Eqs.\ref{eq18} hold.

Completing the proof.

\textbf{Theorem 2:} the $\Phi$KFM sequence production model robustness satisfies the following conditions:

(1) Let the $f_{DNN}$ be DNN sequence generator, then, Lipschitz continuity of the $f_{DNN}$ should be satisfied:

\begin{equation}\label{eq20}
\exists L>0,\forall s,\Delta s, \|f_{DNN}(s+\Delta s)-f_{DNN}(s)\|\le L\|\Delta s\|
\end{equation}

where $L$ is the Lipschitz constant determined by the network weights $W$ and activation functions $\sigma(\cdot)$, the $s$ is the input of $f_{DNN}$.

(2) In PGM, let error be $e_t=\mathbf{x}_t-{\hat{\mathbf{x}}}_t$, and covariance is $P_t=\mathbb{E}[ee^T]$, the posterior covariance matrix $P_t$ satisfies:
\begin{equation}\label{eq21}
\text{tr}(P_t)\le \text{tr}(P_{t-1})
\end{equation}

\textbf{Proof:}

(1) Lipschitz continuity of the DNN Generator.

Let the $f_{DNN}$ be parameterized by weight matrices $W$. The ReLU function $\sigma(\cdot)$ has a gradient satisfying: $\|\nabla\sigma(\cdot)\|\le1$, giving it a Lipschitz constant of 1. This means ReLU does not amplify input perturbations. If the operator norm is defined as:

\begin{equation}\label{eq22}
\|W\|_{op}=\sup_{x\neq0}\frac{\|Wx\|}{\|x\|}
\end{equation}

which quantifies the maximum "amplification factor" of the matrix on input perturbations. For example, if $\|W\|_{op}=2$, an input perturbation $\nabla x$ is amplified at most by a factor of 2 after passing through $W$.

Assume the $f_{DNN}$ has $N$ layers, each with weight matrix $W_i$ and ReLU activation $\sigma(\cdot)$, we have:
\begin{equation}\label{eq23}
f_{DNN}=f_{DNN}^N\circ f_{DNN}^{N-1}\circ\cdots\circ f_{DNN}^1
\end{equation}

For $i$-th layer, $f_{DNN}^i(z)=W_i\cdot\sigma(z)$, according to chain rule, the input gradient is: $\nabla_sf_{DNN}(s)=J_N\cdot J_{N-1}\cdots J_1$, where $J_k$ is Jacobi Matrix, can be defined as: $J_k=\nabla_{z_{k-1}}f_{DNN}(z_{k-1})$, the operator norm of $J_k$: $\|J_k\|_{op}\le\|W_k\|_{op}$, hence:

\begin{equation}\label{eq24}
\|\nabla_sf_{DNN}(s)\|_{op}\le\prod_{k=1}^{N}\|W_k\|_{op}=L
\end{equation}

where $L$ is Lipschitz constant. For proposed SLG model, we have:

\begin{align}\label{eq25}
f_{DNN}(s+\Delta s)-f_{DNN}(s) &= \int{\nabla_sf_{DNN}(s+\Delta s)\cdot\Delta s \, dt} \nonumber \\
&\le \|\nabla_sf_{DNN}(s+\Delta s)\|_{op}\cdot\|\Delta s\| \, dt
\end{align}

Because $\|\nabla_sf_{DNN}(s+\Delta s)\|_{op}\le L$, thus, $\forall s\in\mathcal{S}$:

\begin{equation}\label{eq26}
\|f_{DNN}(s+\Delta s)-f_{DNN}(s)\|\le L\cdot\|\Delta s\|
\end{equation}

(2) Noise Robustness via PGM

The physics equation of PGM is:
\begin{align}\label{eq27}
\mathbf{x}_t &= A\mathbf{x}_{t-1}+Bu_{t-1}+w_{t-1} \nonumber \\
{\hat{k}}_t &= H\mathbf{x}_t+v_t
\end{align}

where $A$, $B$ and $H$ are state matrix, control matrix, and observation matrix, respectively. The $w_{t-1}\sim\mathcal{N}(0,Q)$, $v_t\sim\mathcal{N}(0,R)$, where $Q,R\succ0$ are the state noise covariance matrix and observation noise covariance matrix, respectively. Based on Woodbury inequality \cite{ref10}, we have:
\begin{equation}\label{eq28}
P_t=\left(\left(AP_{t-1}A^T+Q\right)^{-1}+H^TR^{-1}H\right)^{-1}
\end{equation}

Let $Y_{t-1}=P_{t-1}^{-1}$, then:
\begin{equation}\label{eq29}
Y_t=A^{-T}Y_{t-1}A^{-1}+Q^{-1}+H^TR^{-1}H
\end{equation}

Due to $H^TR^{-1}H+Q^{-1}>0$, thus: $Y_t>A^{-T}Y_{t-1}A^{-1}$, and:
\begin{equation}\label{eq30}
P_t=Y_t^{-1}<(A^{-T}Y_{t-1}A^{-1})^{-1}=AP_{t-1}A^T
\end{equation}

If $A$ is stability matrix, then:
\begin{equation}\label{eq31}
P_t<AP_{t-1}A^T<P_{t-1}\Rightarrow\text{tr}(P_t)<\text{tr}(P_{t-1})
\end{equation}

Completing the proof.

\subsection{System Learning}
Based on total object function $J(\mathbf{X})$ Eqs.\ref{eq17}, we know, total loss function is $\mathcal{L}=\lambda_1\mathcal{L}_{KL}+\lambda_2\mathcal{L}_{phy}$, one is distribution loss: $\mathcal{L}_{KL}=D_{KL}$, the other is physical constraint loss: $\mathcal{L}_{phy}=\mathbf{U}$.

For $\mathcal{L}_{KL}$, the $p(\mathbf{K}^{real})$ and $q_\Phi\left(\mathbf{X}|\mathbf{S}\right)$ should be as closer as possible. For calculating $\mathcal{L}$, we assume that the $k_t$ obeys the GMM distribution. For a skeleton contains $n$ joints in time $t$, $J_{t,i}=(x_t^i,y_t^i)$ represents the position of the $i$-th joint at time $t$. Assume $J_{t,i}$ obeys a 2-dimensional normal distribution, and $J_{t,i}\sim\mathcal{N}\left(\mathbf{m}_{t,i},\Sigma_{t,i}^{-1}\right)$, where $\mathbf{m}_{t,i}$ and $\Sigma_{t,i}^{-1}$ are the expectation and variance of $J_{t,i}$, respectively. Furthermore, we have: $k_t\sim p_{gmm}\left(k_t\right)=\sum_{i=1}^{n}{\pi_i\cdot\mathcal{N}\left(\mathbf{m}_{t,i},\Sigma_{t,i}^{-1}\right)}$, where $p_{gmm}\left(k_t\right)$ is a GMM distribution, and $\pi_i$ is the coefficient of the $i$-th component. Thus, the distribution of skeleton sequence can be:
\begin{equation}\label{eq32}
q_\Phi\left(\mathbf{X}|\mathbf{S}\right)=\prod_{i=1}^{T}{\kappa_i\cdot p_{gmm}(k_t)}
\end{equation}

where $\Phi$ is a parameter of the sequence distribution, and $\kappa_i$ is the skeleton weight of $k_t$. We can adjust the $\Phi$ using the gradient descent method.

To enable end-to-end differentiable training, the differentiable $f_{KFM}$ employs a hybrid gradient strategy: the forward pass executes standard Kalman filtering and RTS smoothing algorithms, while the backward propagation combines analytical gradients with automatic differentiation (AD). The forward process strictly follows the iterative computations of classical Kalman filtering Eqs.\ref{eq17}-Eqs.\ref{eq10} and RTS smoothing Eqs.\ref{eq11}-Eqs.\ref{eq13}, where the filtering phase computes the posterior state distribution via the predict-update cycle Eqs.\ref{eq10}, and the smoothing phase optimizes the full-sequence state estimate through backward propagation. Gradient computation adopts a hierarchical strategy: at the micro-level, linear operations including state transition ($A\mathbf{x}_{t-1}$), observation mapping ($H\mathbf{x}_t$), and covariance update ($AP_{t-1}A^T+Q$) rely on the chain rule of automatic differentiation frameworks. At the macro-level, analytical gradients handle temporal recursive structures (filtering cycle, RTS smoothing). For the smoothing phase, we derive the gradient of RTS iteration based on the implicit function theorem. Physical constraints are embedded through projected gradients:

\begin{equation}\label{eq33}
\nabla_{\mathbf{x}_t}\mathcal{L}_{phy}=\sum_i\mathbb{I}(\theta_{t,i}\notin[\theta_{min},\theta_{max}])\cdot\frac{\partial x_t}{\partial\theta_{t,i}}
\end{equation}

while acceleration continuity constraints $\|\Delta x_t^3\|<\varepsilon$ introduce differentiable penalties through Huber loss. This implementation allows $f_{KFM}$ to support gradient backpropagation ($\partial\mathcal{L}/\partial\Omega$) while preserving the probabilistic semantics of classical Kalman filtering, enabling co-optimization of physical constraints and generation quality as formalized in Theorem 1. Based on above, we write $\Phi$KFM training algorithm as:

\begin{algorithm}[t]
\caption{$\Phi$KFM Training with Physical Constraints}
\label{alg:phikfm}
\begin{algorithmic}[1]
\item[] \textbf{Input:} Text corpus with paired motion: $\{(\mathbf{S}^{(i)},\mathbf{K}^{real(i)})\}_{i=1}^N$, Physical constraints $\mathbf{U}: \{\theta_{min},\theta_{max},\varepsilon_1,\varepsilon_2\}$, Hyperparameters: Initial $\lambda_0$, learning rate $\eta$, epochs $E$
\item[] \textbf{Output:} Trained $\Phi$KFM parameters $\Phi = \{\theta_e,\theta_d,\Omega\}$

\item[] \textbf{Initialize:}
\STATE DNN encoder $f_e(\cdot;\theta_e)$, decoder $f_d(\cdot;\theta_d)$ with Xavier initialization
\STATE Differentiable KFM filter $f_{KFM}(\cdot;\Omega)$
\STATE Set $\lambda \gets \lambda_0$, optimizer $\gets$ Adam$(\Phi = \{\theta_e,\theta_d,\Omega\}, \eta)$

\FOR{epoch $= 1$ to $E$}
    \STATE Update $\lambda \gets \lambda_0 \times \tanh(\text{epoch}/10)$
    \FOR{batch $\{(\mathbf{S}^{(b)},\mathbf{K}^{real(b)})\}_{b=1}^B$}
        \STATE Encode text: $\mathbf{h} \gets f_e(\mathbf{S}^{(b)};\theta_e)$
        \STATE Generate motion: $\hat{\mathbf{K}} \gets f_d(\mathbf{h};\theta_d)$
        \STATE Physical refinement: $\mathbf{X} \gets f_{KFM}(\hat{\mathbf{K}},\mathbf{U};\Omega)$

        \STATE \textbf{/*Loss computation*/}
        \STATE Calculate KL divergence:
        \STATE $\mathcal{L}_{KL} \gets \frac{1}{B}\sum_{b=1}^{B} D_{KL}(p(\mathbf{K}^{real(b)}|\mathbf{S}) \parallel q_\Phi(\mathbf{X}^{(b)}|\mathbf{S}))$

        \STATE Calculate physical constraint:
        \STATE $\mathcal{L}_{phy} \gets \frac{1}{BT}\sum_{b=1}^B \left[ \sum_{t=2}^T \sum_{i=1}^n \max\{|J_{t,i}-J_{t-1,i}|-\varepsilon_1,0\} \right.$
        \STATE $\left. + \sum_{t=2}^T \sum_{i=1}^n \max\{|\dot{J}_{t,i}-\dot{J}_{t-1,i}|-\varepsilon_2,0\} + \sum_{i=1}^n \mathbb{I}(\theta_{t,i}\notin[\theta_{min},\theta_{max}]) \right]$

        \STATE Total loss: $\mathcal{L} \gets \lambda_1\mathcal{L}_{KL} + \lambda_2\mathcal{L}_{phy}$

        \STATE \textbf{/*Backpropagation*/}
        \STATE Compute gradients: $\nabla_\Phi\mathcal{L}$
        \STATE Update parameters: $\Phi \gets \text{Adam}(\nabla_\Phi\mathcal{L}, \eta)$
    \ENDFOR
\ENDFOR
\RETURN $\Phi = \{\theta_e, \theta_d, \Omega\}$
\end{algorithmic}
\end{algorithm}

During the testing process, given the training data $\mathbf{K}^{real}$ and the text label $\mathbf{S}$, the classifier $f_c$ are trained, and the parameters $\theta_c$ are learned by the minimum loss function $\mathcal{L}_{class}$, as shown in Fig.\ref{fig1}(b). Then, the recognizable of all generated data can be determined by classifier. 

\section{Experiments}
\subsection{Dataset}

Three datasets were used to evaluate generation performance. The first dataset is CSL500 \cite{ref2}, the largest CSL dataset with 500 isolated CSL words performed by 50 actors, repeated 5 times each, totaling 125,000 skeleton sequence samples. This dataset includes 3D skeletal samples augmented with inverse kinematics-derived ground-truth joint angles, joint velocities and accelerations, and biomechanical constraint boundaries. These extended annotations were validated using a Vicon motion capture system, achieving an average error of less than 0.5°, making the dataset well-suited for evaluating physical constraints.

The second dataset is the word-level American Sign Language (WLASL) dataset \cite{ref3}, containing about 2,000 SL words by over 100 actors. The third dataset is the Kinetics human action dataset \cite{ref4}, comprising 300,000 YouTube videos with 400 human action classes, each video lasting approximately 10 seconds. As these datasets only have RGB videos, OpenPose was used to estimate joint locations for input. OpenPose detects 15 upper-body joints and 21 hand joints per hand, forming a 25-joint skeleton (Fig. \ref{fig2}(a)). For SLG, only upper-body joints were used for motion gestures, simplifying data processing. Fuzzy clustering extracted key frames to create T-frame length skeleton sequences from each video, ensuring good SLG performance. Each dataset was split randomly into training (70\%), cross-validation (15\%), and test sets (15\%).

\subsection{Baseline}
We compare our method with 3 state-of-the-art SLG approaches: (1) Latent Motion Transformer (LMT) \cite{ref5}. LMT introduces a sequence-generation framework based on a hierarchical variational autoencoder (VAE) combined with a Transformer architecture. (2) Spatio Temporal Adversarial attention (ST-AT) \cite{ref6}. The ST-AT integrates adversarial training with a spatiotemporal attention mechanism. The model optimizes the naturalness of generated movements through adversarial losses. (3) Progressive \cite{ref7}. The Progressive employs a hierarchical Graph Neural Network (GNN) architecture to jointly model joint-level physical constraints and sentence-level semantic dependencies. 

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_3.pdf}
\caption{Illustration of the SL skeleton representation. (a) shows the original skeleton in CSL500 with 25 joints, which can be reduced to 14 joints. (b) shows the initially detected skeleton from the RGB image using OpenPose. It contains 57 joints, which can be reduced to 50 joints. (c) shows an example of the skeleton sequence (50 joints, contains hand shape content, and the CSL word is "Future").}\label{fig3}
\end{figure}

\subsection{Quantitative Results}

Based on the datasets and baseline models described above, we conducted comparative experiments, including analyses such as biomechanical compliance evaluation, and so on. We analyzed each experimental result, offering mechanistic interpretations closely aligned with the core innovations of $\Phi$KFM (the physical constraints of $\Phi$KFM, loss function design, and robustness theorem), thereby uncovering methodological insights underlying the data.

\subsubsection{Recognizability}

To comprehensively evaluate the generation quality, we conducted quantitative experiments on recognizability across 3 datasets, with comparative results presented in Table.\ref{tab1}. 

The recognizability experiment requires that generated SL can be accurately interpreted by professional recognition systems. To assess the recognizability of generated sequences, we used a pre-trained SL classifier (as shown in Fig.~\ref{fig1}(b)) to classify the generated samples, with Top-1 accuracy (Acc\%) as the evaluation metric. The test set was constructed by randomly sampling 500 generated samples from each dataset. On CSL500, $\Phi$KFM achieved an accuracy of 85.3\%, significantly outperforming the best baseline Progressive (82.1\%). This improvement stems from two key factors: First, physical constraints ensure joint discernibility. KFM's acceleration smoothing (Eq.~\ref{eq14}) mitigates motion blur (e.g., elbow trembling), making critical gestures easier to recognize. Second, the attention mechanism enhances semantic alignment. The ${cv}_t$ module in Eq.~\ref{eq2} focuses on key terms (e.g., "book" requiring flat, open hands), reducing ambiguous movements. On WLASL, $\Phi$KFM (79.7\%) outperformed ST-AT by 4.2\%, demonstrating its robustness to complex gestures (e.g., finger-spelling in ASL). Even on Kinetics, where scene diversity is high, KFM maintained a 71.2\% accuracy, confirming its strong cross-domain adaptability.

\begin{table}[t]
\centering
\begin{tabular}{l c c c c}
\hline
Dataset & LMT & ST-AT & Progressive & $\Phi$KFM \\ %% A tabular row ends with \\
\hline
WLASL & 72.4 & 75.7 & 73.5 & 79.7 \\
Kinetics & 69.8 & 70.6 & 70.3 & 71.2 \\
CSL500 & 75.6 & 78.3 & 82.1 & 85.3 \\
\hline
\end{tabular}
\caption{Generated Recognition Accuracy Comparison (\%)}\label{tab1}
\end{table}

\subsubsection{Biomechanical Compliance Evaluation}

The Table.\ref{tab2} presents the comparative results between the 3 baseline models and our method on the CSL500 dataset concerning biomechanical compliance. We evaluate biomechanical indicators through the following metrics:

(1) Joint Angle Compliance Rate ($R_c$). Based on the true range of 25 joint angles $\left(\theta_{\min},\theta_{\max}\right)$ provided by the CSL500 dataset, we calculate the percentage of frames in the generated sequences where all joint angles adhere to these constraints, let compliance rate be $R_C$:

\begin{equation}\label{eq34}
R_C=\frac{1}{T\times M}\sum_{t=1}^{T}{\sum_{i=1}^{M}\mathbb{I}\left(\theta_{\min}\le\theta_{t,i}\le\theta_{\max}\right)}\times100\%,
\end{equation}

where $T$ is the total number of frames, $M$ is the number of joints, and $\mathbb{I}(\bullet)$ is the indicator function.

(2) Abrupt Acceleration Change Ratio ($R_A$). We measure the proportion of frames where the difference in joint accelerations between consecutive frames exceeds a biomechanically permissible threshold ($\varepsilon$), specifically calibrated as 0.8 $rad/s^2$ in CSL500:

\begin{equation}\label{eq35}
R_A=\frac{1}{T-1}\sum_{t=2}^{T}\mathbb{I}\left(\|\ddot{J}_t-\ddot{J}_{t-1}\|_2>\varepsilon\right)\times100\%,
\end{equation}

where $\ddot{J_t}$ denotes joint acceleration at time $t$, $\|\cdot\|_2$ represents the Euclidean ($L_2$) norm.
(3) Velocity Continuity Error ($V_E$). We quantify velocity discontinuities using the root mean squared error (RMSE):

\begin{equation}\label{eq36}
V_E=\sqrt{\frac{1}{T-1}\sum_{t=2}^{T}{\|\dot{J}_t-\dot{J}_{t-1}\|}_2^2}.
\end{equation}

The unit is millimeters per frame (mm/frame), with ground-truth velocities calibrated by a Vicon motion capture system.

\begin{table}[t]
\centering
\begin{tabular}{l c c c c}
\hline
Dataset & LMT & ST-AT & Progressive & $\Phi$KFM \\
\hline
WLASL & 72.4 & 75.7 & 73.5 & 79.7 \\
Kinetics & 69.8 & 70.6 & 70.3 & 71.2 \\
CSL500 & 75.6 & 78.3 & 82.1 & 85.3 \\
\hline
\end{tabular}
\caption{Biomechanical Compliance Comparison (CSL500)}\label{tab2}
\end{table}

Based on the test comparison results in Table.\ref{tab2}, our proposed method demonstrates advantages in 3 key points. Firstly, $\Phi$KFM shows significantly superior joint angle compliance, achieving a rate of 91.8\% and notably outperforming the baseline at 88.1\%. This validates the effectiveness of the KFM in enforcing angle boundary constraints. Secondly, the $R_A$ is remarkably low at just 8.3\%, which is 4.2\% lower than the best baseline. This clearly indicates that our state equation (Eqs.\ref{eq14}) effectively enhances motion smoothness. Finally, the $V_E$ is reduced by 0.73 to 2.02 mm/frame when compared to the baseline. This improvement is attributed to the KFM's ability to suppress high-frequency jitter generated by the DNN through its state smoothing capabilities (Eqs.\ref{eq13}).

\subsubsection{Evaluation of Temporal Naturalness}

We also conducted a comparative analysis of the temporal naturalness of the generated SL data, with results presented in Table.\ref{fig3} alongside 3 baseline models. Across all 3 datasets, our method consistently achieved optimal temporal naturalness.

Our method for obtaining the Mean Opinion Score (MOS) involved evaluating generated SL skeleton sequence animations, not raw data or text. These were compared against sequences produced by baseline models and our model. The 30 evaluators, including SL experts, general observers, and researchers with basic SL comprehension, participated. The evaluation process was a double-blind test to prevent bias, meaning evaluators were unaware of the generating model for each animation. Each evaluator independently assigned a score based on temporal naturalness using a 5-point Likert scale. This scale ranged from 5 points ("Indistinguishable from human motion-fluent, natural") down to 1 point ("Completely unacceptable"), with intermediate points for varying degrees of naturalness or unnaturalness. The scoring specifically focused on temporal naturalness, encompassing motion fluency (smoothness of joint movement, e.g., continuous acceleration), rhythmic appropriateness (whether gesture transitions matched linguistic rhythm, e.g., pauses), and physical realism (adherence to human movement principles, e.g., joint angle limits and velocity continuity). Finally, the MOS was calculated by averaging all evaluator scores for all sequences generated by a given model:

\begin{equation}\label{eq37}
\text{MOS}=\frac{1}{N}\sum_{i=1}^{N}\text{Rating}_i,
\end{equation}

where $N$ represented the total number of evaluator ratings (e.g., 30 evaluators$\times$100 samples=3000 ratings).

\begin{table}[t]
\centering
\begin{tabular}{l c c c c}
\hline
Dataset & LMT & ST-AT & Progressive & $\Phi$KFM \\
\hline
WLASL & 3.8 & 4.2 & 4.3 & 4.5 \\
Kinetics & 3.5 & 3.9 & 4.0 & 4.3 \\
CSL500 & 4.1 & 4.3 & 4.4 & 4.7 \\
\hline
\end{tabular}
\caption{Mean Opinion Score (MOS) for Temporal Naturalness}\label{tab3}
\end{table}

We also present a box plot illustrating the MOS score distribution and a comparison of the computational efficiency for each algorithm, as shown in Fig.\ref{fig3}.

Several key conclusions can be drawn from these visualizations. There's a clear trade-off between perceived quality and computational cost. As shown in Fig.\ref{fig3}(a), the $\Phi$KFM consistently achieves a higher MOS (4.7) compared to Progressive (4.4), with its advantage being particularly significant in complex scenarios like Kinetics (0.3point increase). This superior temporal naturalness, however, comes with a latency cost, as shown in Fig.\ref{fig3}3(b). The $\Phi$KFM adds 14ms of filtering computation compared to other methods. Our results indicate that, with the current configuration, we gain a 15\% MOS improvement at the expense of a 22\% increase in latency. Furthermore, the MOS distribution (Fig.\ref{fig3}(a)) shows an Interquartile Range (IQR) of [4.5, 4.7], which serves as a strong validation of robustness, indicating consistent agreement among all 30 evaluators regarding the naturalness of $\Phi$KFM's motion.

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_4.pdf}
\caption{Evaluation of temporal naturalness. (a) The MOS Score Distribution (Based on 3,000 ratings from 30 evaluators) and (b) Algorithmic Efficiency Comparison.}\label{fig4}
\end{figure}

\subsubsection{Component Contribution Analysis}

We also analyzed the contribution of individual components, and the experimental comparison results are shown in Fig.4.

Further analysis of the experimental results reveals key insights. The semantic alignment role of Attention is evident, removing Attention leads to a 0.17 increase in $\mathcal{L}_{KL}$. This rise is because ${cv}_t=\sum{\alpha_{ti}h_i}$ becomes ineffective, resulting in text-motion mismatch. For instance, when generating "rapid head shake," the neck's range of motion is insufficient because the absence of Attention means the model fails to focus on the "rapid" keyword, leading to a lack of sufficient information in the generated output. Furthermore, the quantifiable error correction capability of KFM is significant. Without KFM, the compliance rate is only 83.5\%, but after KFM correction, it dramatically improves to 91.8\%. This demonstrates two crucial points: (a) the initial output $\hat{\mathbf{K}}$ from the DNN contains systematic biases, and (b) KFM effectively compresses the feasible domain to $\mathcal{X}={\mathbf{X}\mid\theta_{min}\le\theta_{t,i}\le\theta_{max}}$ through its physical constraints, as described in Theorem 1.

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_5.pdf}
\caption{Component contribution analysis experimental results.}\label{fig5}
\end{figure}

\subsubsection{Loss Function Analysis}

We also conducted an experimental analysis of the loss functions, and the results are presented in Table.\ref{tab4}.

\begin{table}[t]
\centering
\begin{tabular}{l c c c}
\hline
$\lambda_1:\lambda_2$ & $\mathcal{L}_{KL}$ & $\mathcal{L}_{phy}$ & MOS \\
\hline
1.0:0.0 & 0.29 & 1.54 & 4.1 \\
0.5:0.5 & 0.35 & 0.41 & 4.6 \\
0.3:0.7 & 0.31 & 0.52 & 4.7 \\
\hline
\end{tabular}
\caption{Loss Weight Ablation}\label{tab4}
\end{table}

Through further analysis, we can discern two key insights. First, the implicit guiding role of the physical loss is significant, even when $\lambda_2=0$, $\mathcal{L}_{phy}$ reaches as high as 1.54, yet the DNN can still generate sequences with an 82.1\% compliance rate, indicating that the KFM can independently correct violations. However, when $\lambda_2>0$, the compliance rate of the DNN's initial output $\hat{\mathbf{K}}$ improves by 37\%, substantially reducing the KFM's correction burden. Second, there's a dynamic interplay between KL divergence and physical loss. When $\lambda_1:\lambda_2=0.3:0.7, \mathcal{L}_{KL}$ is at its lowest (0.31). This suggests that physical constraints guide the DNN to explore a more compact semantic motion space, which, counterintuitively, enhances fidelity.

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_6.pdf}
\caption{Analysis of loss function ablation experiment results.}\label{fig6}
\end{figure}

We conducted an ablation study on the loss function, with results visualized in Fig.\ref{fig6}. From these, several key conclusions emerge. The Attention mechanism primarily impacts motion-text alignment, as evidenced by a 0.17 increase in $D_{KL}$ upon its removal, while having minimal effect on physical constraints. More critically, the core value of KFM is undeniable: it significantly boosts physical compliance rates by 13.3\% for joint angles and optimizes motion continuity by 62.7\% by reducing velocity error, proving the PGM module's indispensable nature. Our analysis also identified the optimal loss weights. The KL loss $(\mathcal{L}_{KL})$ monotonically decreases as $\lambda_1$ increases (Fig.\ref{fig5}(a)), yet optimizing it in isolation can lead to a drastic rise in physical loss $(\mathcal{L}_{phy}) $(Fig.\ref{fig5}(d)). Conversely, the physical loss $\mathcal{L}_{phy}$ shows rapid convergence when $\lambda_2>0.5$ (Fig.\ref{fig5}(b)), validating the optimizability of the constraint $u_t$ (Eqs.\ref{eq6}). We pinpointed a Pareto optimum at $\lambda_1:\lambda_2=0.3:0.7$, where the MOS reaches its maximum value of 4.7 (Fig.\ref{fig5}(c)) and the total compliance rate hits 91.8\% (Fig.\ref{fig5}(d)). From an engineering perspective, the loss surface clearly indicates that the system enters a highly sensitive region when $\lambda_2<0.4$, necessitating cautious adjustment of weights in that range.

\subsubsection{Constraint Violation Analysis}

Our constraint violation analysis experiment included fault mode statistics, the results of which are shown in Fig.\ref{fig7}. A key insight from this is a Pareto analysis revealing that 80\% of the issues are concentrated in angle excursions (54 instances) and acceleration jerks (30 instances). Furthermore, certain vulnerable joints are high-incidence areas for angle excursions, notably the shoulder (42\%) and elbow (28\%) joints (Fig.\ref{fig7}(b)). This points towards a crucial direction for improvement: establishing a mapping mechanism from text to physical feasibility.

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_7.pdf}
\caption{Fault mode statistics in constraint violation analysis.}\label{fig7}
\end{figure}

\subsubsection{Analysis of KFM Probability Inference}

The Fig.\ref{fig8} illustrates how the KFM enhances the posterior distribution during SLG. It compares the initial generation $(\hat{\mathbf{K}})$ from the DNN with the KFM-optimized result. The graph shows the posterior probability $(q_\Phi(\mathbf{X}|\mathbf{S}))$ of the generated sequence over time. At the beginning $(t=0)$, the initial DNN output exhibits significant uncertainty, as indicated by the wide spread of the posterior distribution. This indicates that the raw output from the DNN is not sufficiently constrained, resulting in less accurate predictions of joint positions and velocities. However, as the KFM optimization process is applied (frame 25), the posterior distribution is progressively refined, leading to a sharper and more concentrated probability around the true values. This optimization is particularly evident at key time steps (highlighted by the orange circles), where KFM progressively enforces biomechanical constraints on the generated motion, such as the elbow velocity constraint $(t=25)$, the wrist angle constraint $(t=50)$, and the shoulder acceleration constraint $(t=75)$.

\begin{figure}[t]%% placement specifier
\centering%% For centre alignment of image.
\includegraphics[width=\textwidth]{Figure_8.pdf}
\caption{Experimental analysis of KFM probability inference.}\label{fig8}
\end{figure}

\section{Conclusion}

In this paper, we proposed the $\Phi$KFM framework, which integrates attention-enhanced recurrent neural networks with a differentiable Kalman filtering model for SLG. Our method effectively addresses the challenges of biomechanical compliance, temporal naturalness, and linguistic fidelity. Experimental results show that $\Phi$KFM outperforms existing methods, achieving superior joint angle compliance, reduced acceleration jerks, and improved velocity continuity, all while maintaining high temporal naturalness.

The attention mechanism plays a key role in ensuring accurate motion-text alignment, while the KFM ensures that the generated motions adhere to physical constraints. Furthermore, $\Phi$KFM demonstrates strong robustness across various datasets, highlighting its generalizability.

Future work will focus on improving text-to-motion encoding and optimizing computational efficiency, further advancing SLG and other physically-constrained sequence generation tasks.


\begin{thebibliography}{99}

%% For numbered reference style
%% \bibitem{label}
%% Text of bibliographic item

\bibitem{ref1}
Yu, Z., Huang, S., Cheng, Y., \& Birdal, T.
\textit{SignAvatars: A Large-scale 3D Sign Language Holistic Motion Dataset and Benchmark}.
arXiv:2310.20436, 2023.

\bibitem{ref2}
Li, Y., Zhang, Y., Zhao, Z., et al.
\textit{CSL: A large-scale Chinese scientific literature dataset}.
arXiv:2209.05034, 2022.

\bibitem{ref3}
Deng, Z., Leng, Y., Chen, J., et al.
\textit{TMS-Net: A multi-feature multi-stream multi-level information sharing network for skeleton-based sign language recognition}.
Neurocomputing, 2024, 572: 127194.

\bibitem{ref4}
Koller, O., Zargaran, S., Ney, H., et al.
\textit{Deep Sign: Enabling Robust Statistical Continuous Sign Language Recognition via Hybrid CNN-HMMs}.
Int J Comput Vis, 2018, 126: 1311–1325.

\bibitem{ref5}
Xie, P., Peng, T., Du, Y., \& Zhang, Q.
\textit{Sign Language Production with Latent Motion Transformer}.
2024 IEEE/CVF Winter Conference on Applications of Computer Vision (WACV), Waikoloa, HI, USA, 2024, pp. 3012-3022.

\bibitem{ref6}
Renuka, D. K., Kumar, L. A., Harini, K. R., et al.
\textit{Sign Language Production Using Generative AI}.
International Conference on Computing and Intelligent Reality Technologies (ICCIRT), Coimbatore, India, 2024, pp. 33-38.

\bibitem{ref7}
Saunders, B., Camgöz, N. C., \& Bowden, R.
\textit{Progressive Transformers for End-to-End Sign Language Production}.
ECCV, 2020, pp. 687-705.

\bibitem{ref8}
Arib, S. H., Akter, R., Rahman, S., \& Rahman, S.
\textit{SignFormer-GCN: Continuous sign language translation using spatio-temporal graph convolutional networks}.
PLoS One, 2025, 20(2): e0316298.

\bibitem{ref9}
Chaudhary, L., Ananthanarayana, T., Hoq, E., et al.
\textit{Signnet ii: A transformer-based two-way sign language translation model}.
IEEE Transactions on Pattern Analysis and Machine Intelligence, 2022, 45(11): 12896-12907.

\bibitem{ref10}
Zhou, B., Chen, Z., Clapés, A., et al.
\textit{Gloss-Free Sign Language Translation: Improving from Visual-Language Pretraining}.
arXiv:2307.14768, 2023.

\bibitem{ref11}
Lin, K., Wang, X., Zhu, L., et al.
\textit{Gloss-Free End-to-End Sign Language Translation}.
arXiv:2305.12876, 2023.

\bibitem{ref12}
Yang, X., Lim, Z., Jung, H., et al.
\textit{Estimation of Finite Finger Joint Centers of Rotation Using 3D Hand Skeleton Motions Reconstructed from CT Scans}.
Appl. Sci., 2020, 10: 9129.

\bibitem{ref13}
Qazi, A., et al.
\textit{ExerAIde: AI-assisted Multimodal Diagnosis for Enhanced Sports Performance and Personalised Rehabilitation}.
In CVPR Workshops, 2024.

\bibitem{ref14}
Sartinas, E. G., Psarakis, E. Z., \& Kosmopoulos, D. I.
\textit{Motion-based sign language video summarization using curvature and torsion}.
arXiv:2305.16801, 2023.

\bibitem{ref15}
Zhang, H., Goodfellow, I., Metaxas, D., et al.
\textit{StyleSwin: Transformer-Based GAN for High-Resolution Image Generation}.
In CVPR, 2022.

\bibitem{ref16}
Chen, L., Xu, Y., Zhu, Q.-X., \& He, Y.-L.
\textit{Adaptive Multi-Head Self-Attention Based Supervised VAE for Industrial Soft Sensing With Missing Data}.
IEEE Transactions on Automation Science and Engineering, 2024, 21(3): 3564-3575.

\bibitem{ref17}
Yuan, W., et al.
\textit{MoGenTS: Motion Generation based on Spatial-Temporal Joint Modeling}.
In NeurIPS, 2024.

\bibitem{ref18}
Shlezinger, N., et al.
\textit{AI-Aided Kalman Filters}.
arXiv:2410.12289, 2025.

\bibitem{ref19}
Cranmer, M., Greydanus, S., Hoyer, S., et al.
\textit{Lagrangian neural networks}.
arXiv:2003.04630, 2020.

\bibitem{ref20}
Jin, X.-B., Chen, W., Ma, H.-J., et al.
\textit{Parameter-Free State Estimation Based on Kalman Filter with Attention Learning for GPS Tracking in Autonomous Driving System}.
Sensors, 2023, 23(20): 8650.

\bibitem{ref21}
Villa-Monedero, M., Gil-Martín, M., Sáez-Trigueros, D., et al.
\textit{Sign Language Dataset for Automatic Motion Generation}.
Journal of Imaging, 2023, 9(12): 262.

\bibitem{ref22}
Feng, S., Li, X., Zhang, S., et al.
\textit{A review: state estimation based on hybrid models of Kalman filter and neural network}.
Systems Science \& Control Engineering, 2023, 11(1): 2173682.

\bibitem{ref23}
Zhang, H., Goodfellow, I., Metaxas, D., \& Odena, A.
\textit{StyleSwin: Transformer-Based GAN for High-Resolution Image Generation}.
In CVPR, 2022.

\bibitem{ref24}
Natarajan, B., \& Elakkiya, R.
\textit{Dynamic GAN for High-Quality Sign Language Video Generation from Skeletal Poses Using Generative Adversarial Networks}.
Soft Computing, 2021, 26(24): 12947–12960.

\bibitem{ref25}
Wang, Y., \& Zhang, H.
\textit{Updated Prediction of Air Quality Based on Kalman-Attention-LSTM Model}.
Sustainability, 2023, 15(1): 356.

\bibitem{ref26}
Yu, Z., Huang, S., Cheng, Y., \& Birdal, T.
\textit{SignAvatars: A Large-scale 3D Sign Language Holistic Motion Dataset and Benchmark}.
arXiv:2310.20436, 2023.

\bibitem{ref27}
Dong, L., Wang, X., \& Nwogu, I.
\textit{Word-Conditioned 3D American Sign Language Motion Generation}.
In Findings of the Association for Computational Linguistics: EMNLP 2024, pp. 9993–9999.

\bibitem{ref28}
Ranum, O., Otterspeer, G., Andersen, J. I., Belleman, R. G., \& Roelofsen, F.
\textit{3D-LEX v1.0: 3D Lexicons for American Sign Language and Sign Language of the Netherlands}.
arXiv:2409.01901, 2024.

\bibitem{ref29}
Saunders, B., Camgoz, N. C., \& Bowden, R.
\textit{Everybody Sign Now: Translating Spoken Language to Photo Realistic Sign Language Video}.
arXiv:2011.09846, 2020.

\end{thebibliography}
\end{document}

\endinput
%%
%% End of file `elsarticle-template-num.tex'.
