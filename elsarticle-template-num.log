This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.8.28)  20 JUN 2025 10:31
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/Desktop/Elsarticle/elsarticle/elsarticle-template-num.tex
(c:/Users/<USER>/Desktop/Elsarticle/elsarticle/elsarticle-template-num.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(d:/texlive/2024/texmf-dist/tex/latex/elsarticle/elsarticle.cls
Document Class: elsarticle 2020/11/20, 3.3: Elsevier Ltd
(d:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (d:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count188
\l__pdf_internal_box=\box51
)) (d:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
) (d:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count189
)
\@bls=\dimen140
 (d:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(d:/texlive/2024/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count190
\c@section=\count191
\c@subsection=\count192
\c@subsubsection=\count193
\c@paragraph=\count194
\c@subparagraph=\count195
\c@figure=\count196
\c@table=\count197
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen141
) (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen142
\Gin@req@width=\dimen143
)
\c@tnote=\count198
\c@fnote=\count199
\c@cnote=\count266
\c@ead=\count267
\c@author=\count268
\@eadauthor=\toks18
\c@affn=\count269
\absbox=\box52
\elsarticlehighlightsbox=\box53
\elsarticlegrabsbox=\box54
\keybox=\box55
\Columnwidth=\dimen144
\space@left=\dimen145
\els@boxa=\box56
\els@boxb=\box57
\leftMargin=\dimen146
\@enLab=\toks19
\@sep=\skip50
\@@sep=\skip51
 (./elsarticle-template-num.spl) (d:/texlive/2024/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip52
\bibsep=\skip53
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count270
)
\splwrite=\write3
\openout3 = `elsarticle-template-num.spl'.

\appnamewidth=\dimen147
) (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks20
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip54

For additional information on amsmath, use the `?' option.
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen148
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen149
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box58
\strutbox@=\box59
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen150
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip16
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks22
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks23
\eqnshift@=\dimen151
\alignsep@=\dimen152
\tagshift@=\dimen153
\tagwidth@=\dimen154
\totwidth@=\dimen155
\lineht@=\dimen156
\@envbody=\toks24
\multlinegap=\skip55
\multlinetaggap=\skip56
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (d:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count283
\float@exts=\toks26
\float@box=\box60
\@float@everytoks=\toks27
\@floatcapt=\box61
) (d:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks28
\c@algorithm=\count284
) (d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
\c@ALC@unique=\count285
\c@ALC@line=\count286
\c@ALC@rem=\count287
\c@ALC@depth=\count288
\ALC@tlm=\skip57
\algorithmicindent=\skip58
) (./elsarticle-template-num.aux)
\openout1 = `elsarticle-template-num.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 57.
LaTeX Font Info:    ... okay on input line 57.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 57.
LaTeX Font Info:    ... okay on input line 57.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 57.
LaTeX Font Info:    ... okay on input line 57.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 57.
LaTeX Font Info:    ... okay on input line 57.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 57.
LaTeX Font Info:    ... okay on input line 57.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 57.
LaTeX Font Info:    ... okay on input line 57.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 57.
LaTeX Font Info:    ... okay on input line 57.
 (d:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count289
\scratchdimen=\dimen157
\scratchbox=\box62
\nofMPsegments=\count290
\nofMParguments=\count291
\everyMPshowfont=\toks29
\MPscratchCnt=\count292
\MPscratchDim=\dimen158
\MPnumerator=\count293
\makeMPintoPDFobject=\count294
\everyMPtoPDFconversion=\toks30
) (d:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (d:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
LaTeX Font Info:    Trying to load font information for U+msa on input line 127.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 127.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Overfull \hbox (13.71628pt too wide) in paragraph at lines 126--128
\OT1/cmr/m/n/12 tiotem-po-ral co-her-ence. $^^H$KFM ex-plic-itly in-te-grates phys-i-cal con-straints through
 []


Overfull \hbox (33.83743pt too wide) in paragraph at lines 131--131
$\OT1/cmr/m/n/12 ^^H$\OT1/cmr/bx/n/12 KFM: Physically-Constrained Sign Lan-guage Gen-er-a-tion via Attention-
 []



pdfTeX warning: pdflatex.exe (file ./Graphical Abstract.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Graphical Abstract.pdf, id=1, 584.54384pt x 267.03766pt>
File: Graphical Abstract.pdf Graphic file (type pdf)
<use Graphical Abstract.pdf>
Package pdftex.def Info: Graphical Abstract.pdf  used on input line 133.
(pdftex.def)             Requested size: 390.0pt x 178.16455pt.

Overfull \hbox (33.83743pt too wide) in paragraph at lines 137--137
$\OT1/cmr/m/n/12 ^^H$\OT1/cmr/bx/n/12 KFM: Physically-Constrained Sign Lan-guage Gen-er-a-tion via Attention-
 []

[1

{d:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map} <./Graphical Abstract.pdf>] [2

{d:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}]
Overfull \hbox (2.61108pt too wide) has occurred while \output is active
[] 
 []

[1


]
Overfull \hbox (0.29178pt too wide) in paragraph at lines 171--172
\OT1/cmr/m/n/12 tem-po-ral co-or-di-na-tion and smooth mo-tion tran-si-tions|remains a ma-jor chal-
 []

[2] [3]

pdfTeX warning: pdflatex.exe (file ./Figure_1.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_1.pdf, id=82, 559.73117pt x 668.97928pt>
File: Figure_1.pdf Graphic file (type pdf)
<use Figure_1.pdf>
Package pdftex.def Info: Figure_1.pdf  used on input line 193.
(pdftex.def)             Requested size: 390.0pt x 466.12846pt.

LaTeX Warning: Float too large for page by 25.5729pt on input line 195.

[4] [5]

pdfTeX warning: pdflatex.exe (file ./Figure_2.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_2.pdf, id=96, 356.65245pt x 192.9609pt>
File: Figure_2.pdf Graphic file (type pdf)
<use Figure_2.pdf>
Package pdftex.def Info: Figure_2.pdf  used on input line 269.
(pdftex.def)             Requested size: 390.0pt x 211.01807pt.

Overfull \hbox (32.02687pt too wide) detected at line 296
[]
 []

[6] [7] [8] [9] [10] [11]
Overfull \hbox (4.551pt too wide) in paragraph at lines 482--483
[] []$[]$ 
 []

[12]

pdfTeX warning: pdflatex.exe (file ./Figure_3.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_3.pdf, id=123, 426.03165pt x 347.73915pt>
File: Figure_3.pdf Graphic file (type pdf)
<use Figure_3.pdf>
Package pdftex.def Info: Figure_3.pdf  used on input line 509.
(pdftex.def)             Requested size: 390.0pt x 318.34798pt.
[13] [14] [15]
Overfull \hbox (43.3304pt too wide) in paragraph at lines 590--591
[]\OT1/cmr/m/n/12 where $\OML/cmm/m/it/12 N$ \OT1/cmr/m/n/12 rep-re-sented the to-tal num-ber of eval-u-a-tor rat-ings (e.g., 30 evaluators$\OMS/cmsy/m/n/12 ^^B$\OT1/cmr/m/n/12 100
 []



pdfTeX warning: pdflatex.exe (file ./Figure_4.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_4.pdf, id=134, 755.58286pt x 445.78545pt>
File: Figure_4.pdf Graphic file (type pdf)
<use Figure_4.pdf>
Package pdftex.def Info: Figure_4.pdf  used on input line 612.
(pdftex.def)             Requested size: 390.0pt x 230.09563pt.
[16]

pdfTeX warning: pdflatex.exe (file ./Figure_5.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_5.pdf, id=138, 815.68741pt x 446.26726pt>
File: Figure_5.pdf Graphic file (type pdf)
<use Figure_5.pdf>
Package pdftex.def Info: Figure_5.pdf  used on input line 624.
(pdftex.def)             Requested size: 390.0pt x 213.37509pt.


pdfTeX warning: pdflatex.exe (file ./Figure_6.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_6.pdf, id=139, 727.39754pt x 904.69995pt>
File: Figure_6.pdf Graphic file (type pdf)
<use Figure_6.pdf>
Package pdftex.def Info: Figure_6.pdf  used on input line 650.
(pdftex.def)             Requested size: 390.0pt x 485.06575pt.
[17]

pdfTeX warning: pdflatex.exe (file ./Figure_7.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_7.pdf, id=144, 704.99384pt x 419.76826pt>
File: Figure_7.pdf Graphic file (type pdf)
<use Figure_7.pdf>
Package pdftex.def Info: Figure_7.pdf  used on input line 662.
(pdftex.def)             Requested size: 390.0pt x 232.21832pt.
[18]

pdfTeX warning: pdflatex.exe (file ./Figure_8.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<Figure_8.pdf, id=148, 560.81517pt x 459.39629pt>
File: Figure_8.pdf Graphic file (type pdf)
<use Figure_8.pdf>
Package pdftex.def Info: Figure_8.pdf  used on input line 672.
(pdftex.def)             Requested size: 390.0pt x 319.47226pt.

Overfull \hbox (15.58339pt too wide) in paragraph at lines 678--679
[]\OT1/cmr/m/n/12 In this pa-per, we pro-posed the $^^H$KFM frame-work, which in-te-grates attention-
 []

[19] [20] [21] [22] [23 <./Figure_1.pdf>] [24 <./Figure_2.pdf>] [25] [26 <./Figure_3.pdf>] [27 <./Figure_4.pdf> <./Figure_5.pdf

pdfTeX warning: pdflatex.exe (file ./Figure_5.pdf): PDF inclusion: multiple pdfs with page group included in a single page
>] [28 <./Figure_6.pdf>] [29 <./Figure_7.pdf>] [30 <./Figure_8.pdf>] (./elsarticle-template-num.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 4261 strings out of 476065
 61270 string characters out of 5792787
 1945190 words of memory out of 5000000
 26326 multiletter control sequences out of 15000+600000
 569193 words of font info for 78 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 72i,13n,79p,1519b,513s stack positions out of 10000i,1000n,20000p,200000b,200000s
<d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi6.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr17.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmti10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmti12.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmti7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1200.pfb>
Output written on elsarticle-template-num.pdf (32 pages, 1858556 bytes).
PDF statistics:
 405 PDF objects out of 1000 (max. 8388607)
 244 compressed objects within 3 object streams
 0 named destinations out of 1000 (max. 500000)
 46 words of extra memory for PDF output out of 10000 (max. 10000000)

